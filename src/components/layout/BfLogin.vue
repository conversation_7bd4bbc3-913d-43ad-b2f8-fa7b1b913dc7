<template>
  <div
    id="bflogin"
    :class="bgClass"
    :style="loginStyles"
  >
    <div id="login_model">
      <h3
        class="login-title"
        v-text="$t('loginDlg.account')"
      />
      <el-form>
        <el-form-item>
          <el-input
            v-model="system"
            :placeholder="$t('loginDlg.system')"
            @keydown.enter="inputEnterLogin"
            @change="resetSession"
          >
            <template #prefix>
              <i class="iconfont icon-number" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            id="username"
            v-model="username"
            :placeholder="$t('loginDlg.name')"
            :autofocus="true"
            @keydown.enter="inputEnterLogin"
            @change="resetSession"
          >
            <template #prefix>
              <i class="iconfont icon-login-user" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            id="user_pwd"
            ref="password"
            v-model="password"
            :placeholder="$t('loginDlg.password')"
            type="password"
            @keydown.enter="inputEnterLogin"
            @change="resetSession"
          >
            <template #prefix>
              <i class="iconfont icon-password" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="remember">
            <span v-text="$t('loginDlg.remember')" />
          </el-checkbox>
          <p class="text-red-400 text-lg text-right  w-full">
            {{ statusTip }}
          </p>
        </el-form-item>
        <el-form-item>
          <el-button
            class="login_button"
            type="primary"
            :disabled="disSignIn"
            @click="loginNow"
          >
            {{ $t('loginDlg.login') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
  import bfStorage from '@/utils/storage'
  import '@/css/iconfont/iconfont.css'
  import login, { syncServerTime } from '@/utils/login'
  import { loadLanguageAsync, fixLang } from '@/modules/i18n'
  import { saveLicense } from '@/utils/bfAuth'
  import { BASE_URL } from '@/envConfig'
  import packageJson from '~/package.json'
  import { compare } from 'compare-versions'

  const minServerVersion = packageJson.minServerVersion
  bfglob.console.log('minServerVersion', minServerVersion)

  export default {
    data() {
      return {
        remember: true,
        system: '',
        username: '',
        password: '',
        connectStats: 1000,
        disSignIn: true,
        isLogin: false,
      }
    },
    methods: {
      // 记住用户账号名,登录时执行
      remembered() {
        if (this.remember) {
          const locale = fixLang(this.$i18n.locale)
          if (locale !== this.$i18n.locale) {
            loadLanguageAsync(locale)
          }
          let account = {
            username: this.username,
            system: this.system,
            password: ''.padStart(this.password.length, '*'),
            sessionId: bfglob.sessionId,
            locale,
          }
          account = JSON.stringify(account)
          bfStorage.setItem('bfdx_account', account)
        } else {
          bfStorage.removeItem('bfdx_account')
        }
      },
      resetSession() {
        bfglob.sessionId = ''
      },
      passwordFocus() {
        this.resetSession()
        this.$refs.password.focus()
      },
      /**
       * 检查版本，如果小于指定的版本，则提示用户升级
       * @param version {string} 服务器当前版本
       */
      checkServerVersionIsNeedUpgrade(version) {
        // 如果没有版本号，则不处理
        if (!version) return
        if (compare(version, minServerVersion, '<')) {
          ElMessage({
            message: this.$t('loginDlg.upgradeServerTip', { latestServerVersion: minServerVersion, currentServerVersion: version }),
            type: 'warning',
            duration: 0,
            showClose: true,
            offset: 60,
          })
        }
      },
      loginResponse(response) {
        bfglob.console.log('loginResponse', response)
        this.connectStats = response.responseCode

        // 100:成功 0:不成功(未知原因) 1:无此用户 2:密码检验不通过 3:无此sid
        switch (response.responseCode) {
          case 0:
            break
          case 1:
            break
          case 2:
            this.passwordFocus()
            break
          case 3:
            this.password = ''
            this.passwordFocus()
            break
          case 100:
            this.checkServerVersionIsNeedUpgrade(response.serverVersion)

            // 设置全局属性设置
            login.saveLoginResponse(response)

            // 缓存账号信息
            this.remembered()
            // 释放变量内存
            window.bfaccount = null

            // nats没有向外提供客户端IP信息，发一次post请求到服务器上，以便服务记录客户端IP
            login.fetchClientIp(response.sid)

            // 保存授权信息
            // 兼容旧版本，如果没有mod-svt授权则补齐
            if (!response.lic.lic?.licenses['mod-svt']) {
              response.lic.lic.licenses['mod-svt'] = 0
            }
            saveLicense(response.lic)

            // 路由跳转
            this.$router.replace('/main')

            break
          default:
            bfglob.console.error('unknown login response:', response)
        }
      },
      loginCatch(err) {
        bfglob.console.error('loginCatch', err)

        this.connectStats = 1004
      },
      loginNow() {
        if (!bfglob.server.wasConnected) {
          this.connectStats = 1002
          return
        }
        if (!this.system || !this.username || !this.password) {
          bfglob.sessionId = ''
          return
        }

        // 标记登录中
        this.connectStats = 1003

        // 判断系统号、用户名、密码变化，清空sessionID
        if (window.bfaccount) {
          if (this.system !== window.bfaccount.system ||
            this.username !== window.bfaccount.username ||
            this.password !== window.bfaccount.password
          ) {
            this.resetSession()
          }
        }

        login.login({
          sysId: this.system,
          userName: this.username,
          password: this.password,
        })
          .then(res => {
            this.loginResponse(res)
          })
          .catch(err => {
            this.loginCatch(err)
          })
      },
      inputEnterLogin() {
        if (this.disSignIn) {
          return
        }
        this.loginNow()
      },

      // 订阅处理服务器连接状态信息
      onConnectedToServer() {
        this.connectStats = 1001
        this.disSignIn = false
        // 同步服务器时间, 不需要登录也可以请求
        syncServerTime()
      },
      onReconnectedToServer() {
        this.onConnectedToServer()
      },
      onDisconnectFromServer() {
        bfglob.isLogin = false
        this.connectStats = 1002
      },
      onServerConnectionError(err) {
        bfglob.console.error(err)
        this.onDisconnectFromServer()
      },
    },
    watch: {
      system(val) {
        bfglob.sysId = val
      },
      username(val) {
        bfglob.userInfo.name = val
      },
    },
    computed: {
      loginStyles() {
        return {
          backgroundImage: `url(${BASE_URL}login.${this.$i18n.locale}.${bfglob.siteConfig?.loginBackgroundImageExt || 'jpg'})`
        }
      },
      bgClass() {
        return `bg_${this.$i18n.locale}`
      },
      statusTip() {
        let tip = ''
        switch (this.connectStats) {
          case 1102:
            tip = this.$t('loginDlg.noLoginPermission')
            break
          case 1000:
            tip = this.$t('loginDlg.connecting')
            break
          case 1001:
            tip = this.$t('loginDlg.connected')
            break
          case 1002:
            tip = this.$t('loginDlg.disconnect')
            break
          case 1:
            tip = this.$t('loginDlg.noHasUser')
            break
          case 2:
            tip = this.$t('loginDlg.passwordError')
            break
          case 3:
            tip = this.$t('loginDlg.oldSession')
            break
          case 1004:
            tip = this.$t('loginDlg.loginTimeout')
            break
          case 1003:
          default:
            tip = this.$t('loginDlg.logging')
        }

        return tip
      },
    },
    beforeMount() {
      // 读取系统缓存账号信息
      if (window.bfaccount) {
        this.system = window.bfaccount.system
        this.username = window.bfaccount.username || window.bfaccount.name || ''
        this.password = window.bfaccount.password ||
          (window.bfaccount.len ? (''.padStart(window.bfaccount.len, '*')) : '')
        bfglob.sessionId = window.bfaccount.sessionId || window.bfaccount.sid || ''
      }

      bfglob.on('server.connect', this.onConnectedToServer)
      bfglob.on('server.reconnect', this.onReconnectedToServer)
      bfglob.on('server.error', this.onServerConnectionError)
      bfglob.on('server.disconnect', this.onDisconnectFromServer)

      login.connectServer()
    },
    beforeUnmount() {
      bfglob.off('server.connect', this.onConnectedToServer)
      bfglob.off('server.reconnect', this.onReconnectedToServer)
      bfglob.off('server.error', this.onServerConnectionError)
      bfglob.off('server.disconnect', this.onDisconnectFromServer)
    },
  }

</script>

<style lang="scss" scoped>
  #bflogin {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1010;
    background-color: #e4e4e4 !important;
    background-size: cover !important;

    #bflogin[class*="bg_"] {
      background-position: center;
      background-repeat: no-repeat;
    }

    #login_model {
      background: linear-gradient(rgba(0, 0, 0, .4), rgba(255, 255, 255, 0.05));
      padding: 15px 30px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      border-radius: 5px;
      /*box-shadow: 0 0 15px #000;*/
      box-sizing: border-box;
      color: #fff;
    }

    #login_model .el-form-item:not(:last-child) {
      margin-bottom: 10px;
    }

    #login_model .login_button {
      width: 100%;
      min-width: 120px;
    }

    #login_model .login-title {
      line-height: 32px;
      margin-bottom: 10px;
      font-size: 1.4em;
      text-align: center;
    }

    @media (min-width: 768px) {
      #login_model {
        width: 420px;
      }

      .el-form .el-form-item {
        margin: 20px 0;
      }
    }

    @media (max-width: 767px) {
      #login_model {
        width: 90%;
      }

      .el-form .el-form-item {
        margin: 16px 0;
      }
    }
  }
</style>
