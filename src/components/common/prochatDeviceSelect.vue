<template>
  <el-form-item class="form-item-ellipsis prochat-device-select">
    <template #label>
      <span
        class="form-item-label"
        :title="$t('dialog.prochatDeviceList')"
      >{{ $t('dialog.prochatDeviceList') }}</span>
    </template>
    <el-select
      v-model="prochatDeviceInfoRef"
      filterable
      :placeholder="$t('dialog.select')"
      :no-match-text="$t('dialog.noMatchText')"
      value-key="selfId"
      @change="onProchatDeviceInfoChanged"
    >
      <el-option
        v-for="(item) in gprochatDeviceInfoListRef"
        :key="item.selfId"
        :label="item.selfId"
        :value="item"
      />
    </el-select>
    <el-button
      type="primary"
      icon="refresh-right"
      @click="queryProchatDeviceInfoList"
    />
  </el-form-item>
</template>

<script setup>
import {
getProchatDeviceInfoList,
gprochatDeviceInfoListRef,
prochatDeviceInfo,
} from '@/utils/prochatDeviceInfoList'
import { debounce } from 'lodash'
import { onMounted, ref } from 'vue'

// Define component name (for debugging purposes)
defineOptions({
  name: 'ProchatDeviceSelect'
})

// Define emits,value is prochatDeviceInfoRef.value
const emit = defineEmits(['update-prochat-info'])

const prochatDeviceInfoRef = ref(prochatDeviceInfo)

// Methods
const queryProchatDeviceInfoList = debounce(getProchatDeviceInfoList, 300)

const onProchatDeviceInfoChanged = (val) => {
  emit('update-prochat-info', val)
}

// Lifecycle - equivalent to created()
onMounted(() => {
  if (!gprochatDeviceInfoListRef.value || gprochatDeviceInfoListRef.value.length === 0) {
    getProchatDeviceInfoList()
  }
  // Since we're directly referencing the global array, no need to manually sync
})
</script>

<style lang="scss">
  .prochat-device-select .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;

    & > .el-select {
      flex: auto;

      .el-select__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    & > .el-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
</style>
