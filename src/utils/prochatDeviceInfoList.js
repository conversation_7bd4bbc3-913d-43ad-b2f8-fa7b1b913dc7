import bfproto, { bfdxProtoPackageName } from '@/modules/protocol'
import { RespCode } from '@/modules/protocol/gen/mesh_pb'
import { Prochat as prochatService } from '@/modules/protocol/gen/prochatrpc_connect'
import { DeviceTypes } from '@/utils/bfutil'
import { createClient, createHeaders } from '@/utils/connectrpc'
import base64js from 'base64-js'
import { ref } from 'vue'

const prochatClient = createClient(prochatService)
const headers = createHeaders()
let msList_ssid, msUpdate_ssid

export const prochatControllerRid = '14141414-1414-1414-1414-141414141414'
export const prochatUserRid = '41414141-4141-4141-4141-414141414141'
export const prochatDeviceInfo = {
  selfId: '',
  priority: 1,
  setting: {
    prochatID: 0,
    prochatName: ''
  }
}
export var gprochatDeviceInfoListRef= ref([])

/**
 *
 * @type {Map<number, string>} prochatID => DmrID
 */
export const gaddedProchatDevice = new Map()

export function initGaddedProchatDevice() {
  let prochatID
  const __gdevices = bfglob.gdevices.getAll()
  for (const i in __gdevices) {
    if (__gdevices[i].deviceType === DeviceTypes.ProchatDevice) {
      prochatID = JSON.parse(__gdevices[i].setting).prochatID
      gaddedProchatDevice.set(prochatID, __gdevices[i].dmrId)
    }
  }
}

/**
 * 转换msInfo到prochatDeviceInfo，以适配prochatDevice对象

 */
export function getProchatDeviceInfo(msInfo) {
  return {
    ...prochatDeviceInfo,
    selfId: msInfo.deviceId,
    priority: msInfo.priority,
    setting: {
      prochatID: msInfo.msID,
      prochatName: msInfo.deviceId
    }
  }
}

export function setProchatDeviceData(device, prochatDeviceInfo) {
  const setting = {
    ...JSON.parse(device.setting),
    ...prochatDeviceInfo.setting
  }

  if (prochatDeviceInfo.priority >= 3) {
    device.priority = 3
  } else {
    device.priority = prochatDeviceInfo.priority
  }
  device.setting = JSON.stringify(setting)
}

/**
 * 向后端请求查找 prochatDevice,结果通过 nats 传回前端。
 * 返回的内容仅包括 prochat 的 msID, msName, priority。
 * 其中 msName 对应 selfId; msID 放在 setting 。
 *
 */
export async function getProchatDeviceInfoList() {
  const commonReq = {
    sid: bfglob.sessionId,
  }
  if (bfglob.server.subs[msUpdate_ssid] === undefined) {
    msList_ssid = await bfglob.server.subscribe('prochat.MTGS_MsList', onReceiveMsList)
  }

  const resp = await prochatClient.queryProchatDeviceList(commonReq, { headers: headers })
  switch (resp.Code) {
    case RespCode.RespCode_OK :
      if (bfglob.server.subs[msUpdate_ssid]) {
        break
      }
      msUpdate_ssid = await bfglob.server.subscribe('prochat.MTGS_MsUpdate', onReceiveMsUpdate)
      break
    case RespCode.RespCode_Err:
      console.warn('queryProchatDeviceList fail:', resp.StrData)
      break
    case RespCode.RespCode_Not_Found:
      console.warn('can not find the prochat gateway.')
      break
  }
}

export function onReceiveMsList(msg_data, msg_reply, msg_subject, nats_ssid) {
  const byteArray = base64js.toByteArray(msg_data)
  const MTGS_MsList = bfproto.decodeMessage(byteArray, 'MTGS_MsList', bfdxProtoPackageName)
  let prochatDeviceInfoTmp, deviceInfoIndex
  if (MTGS_MsList.listEnd === 0) {
    gprochatDeviceInfoListRef.value = []
  }

  window.requestIdleCallback((deadline) => {
    for (const msInfo of MTGS_MsList.msList) {
      deviceInfoIndex = gprochatDeviceInfoListRef.value.findIndex((item) => item.setting.prochatID === msInfo.msID)
      if (deviceInfoIndex === -1) {
        prochatDeviceInfoTmp = getProchatDeviceInfo(msInfo)
        gprochatDeviceInfoListRef.value.push(prochatDeviceInfoTmp)
      }
    }
  }, { timeout: 1000 })

  if (MTGS_MsList.listEnd === 2) {
    bfglob.server.unsubscribe(msList_ssid)
  }
}

export function onReceiveMsUpdate(msg_data, msg_reply, msg_subject, nats_ssid) {
  const byteArray = base64js.toByteArray(msg_data)
  const MTGS_MsUpdate = bfproto.decodeMessage(byteArray, 'MTGS_MsUpdate', bfdxProtoPackageName)
  if (MTGS_MsUpdate.msInfo.msID === 0 && MTGS_MsUpdate.msID === '') {
    return
  }
  switch (MTGS_MsUpdate.operate) {
    case 0:
      const prochatDeviceInfoTmp = getProchatDeviceInfo(MTGS_MsUpdate.msInfo)
      gprochatDeviceInfoListRef.value.push(prochatDeviceInfoTmp)
      break
    case 1:
      break
    case 2:
      const index = gprochatDeviceInfoListRef.value.findIndex(item => {
        return item.setting.prochatID === MTGS_MsUpdate.msInfo.msID
      })
      gprochatDeviceInfoListRef.value.splice(index, 1)
      break
  }
}
